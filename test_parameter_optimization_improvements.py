#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数优化功能改进
包括：
1. 盈利亏损单数字段显示
2. 排序逻辑优化避免重复执行
3. 结果导出下载功能
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_parameter_optimization_improvements():
    """测试参数优化功能改进"""
    print("🧪 开始测试参数优化功能改进...")
    
    # 测试服务器地址
    base_url = "http://localhost:5000"
    
    # 测试数据
    test_data = {
        "model_id": "test_model_123",
        "symbol": "XAUUSD",
        "timeframe": "1h",
        "optimization_period": "week",
        "risk_preference": "balanced"
    }
    
    print(f"📊 测试数据: {test_data}")
    
    # 测试1: 检查前端页面是否包含新字段
    print("\n1️⃣ 测试前端页面改进...")
    try:
        response = requests.get(f"{base_url}/model_inference")
        if response.status_code == 200:
            content = response.text
            
            # 检查是否包含盈利单和亏损单字段
            if "盈利单" in content and "亏损单" in content:
                print("✅ 前端表格已包含盈利单和亏损单字段")
            else:
                print("❌ 前端表格缺少盈利单或亏损单字段")
            
            # 检查是否包含导出按钮
            if "导出结果" in content and "exportOptimizationResults" in content:
                print("✅ 前端已包含导出功能")
            else:
                print("❌ 前端缺少导出功能")
                
        else:
            print(f"❌ 无法访问前端页面: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端页面测试失败: {e}")
    
    # 测试2: 测试API端点是否存在
    print("\n2️⃣ 测试API端点...")
    try:
        # 测试参数优化API（带resort_only参数）
        test_resort_data = test_data.copy()
        test_resort_data["resort_only"] = True
        
        response = requests.post(
            f"{base_url}/api/deep-learning/parameter-optimization",
            json=test_resort_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code in [200, 400, 401]:  # 200成功，400/401参数或认证问题
            print("✅ 参数优化API端点可访问")
            if response.status_code == 200:
                result = response.json()
                if "resort_only" in str(result):
                    print("✅ API支持resort_only参数")
        else:
            print(f"❌ 参数优化API端点异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
    
    # 测试3: 测试导出API端点
    print("\n3️⃣ 测试导出API端点...")
    try:
        response = requests.post(
            f"{base_url}/api/deep-learning/export-optimization-results",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code in [200, 400, 401]:  # 200成功，400/401参数或认证问题
            print("✅ 导出API端点可访问")
            if response.status_code == 200:
                # 检查是否返回CSV内容
                if "text/csv" in response.headers.get("Content-Type", ""):
                    print("✅ 导出API返回CSV格式")
                else:
                    print("⚠️ 导出API未返回CSV格式")
        else:
            print(f"❌ 导出API端点异常: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 导出API端点测试失败: {e}")
    
    # 测试4: 检查深度学习服务中的新方法
    print("\n4️⃣ 测试深度学习服务新方法...")
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 检查是否有新的方法
        if hasattr(deep_learning_service, '_calculate_all_scores'):
            print("✅ 深度学习服务包含_calculate_all_scores方法")
        else:
            print("❌ 深度学习服务缺少_calculate_all_scores方法")
            
        if hasattr(deep_learning_service, 'resort_optimization_results'):
            print("✅ 深度学习服务包含resort_optimization_results方法")
        else:
            print("❌ 深度学习服务缺少resort_optimization_results方法")
            
        if hasattr(deep_learning_service, 'export_optimization_results_csv'):
            print("✅ 深度学习服务包含export_optimization_results_csv方法")
        else:
            print("❌ 深度学习服务缺少export_optimization_results_csv方法")
            
    except Exception as e:
        print(f"❌ 深度学习服务测试失败: {e}")
    
    print("\n🎉 参数优化功能改进测试完成!")
    print("\n📋 改进总结:")
    print("1. ✅ 增加了盈利单数和亏损单数字段显示")
    print("2. ✅ 优化了排序逻辑，避免重复执行回测")
    print("3. ✅ 添加了结果导出下载功能")
    print("\n💡 使用说明:")
    print("- 在模型推理页面执行参数优化后，可以看到盈利单和亏损单数")
    print("- 切换排序方式时会快速重新排序，不会重新执行回测")
    print("- 点击'导出结果'按钮可以下载CSV格式的详细结果")

def test_csv_export_format():
    """测试CSV导出格式"""
    print("\n📄 测试CSV导出格式...")
    
    try:
        from services.deep_learning_service import deep_learning_service
        
        # 模拟测试数据
        test_result = {
            'success': True,
            'optimization_results': [
                {
                    'rank': 1,
                    'score': 85.5,
                    'total_return': 12.5,
                    'win_rate': 65.0,
                    'winning_trades': 13,
                    'losing_trades': 7,
                    'total_trades': 20,
                    'max_drawdown': -5.2,
                    'sharpe_ratio': 1.8,
                    'parameters': {
                        'lot_size': 0.01,
                        'stop_loss_pips': 50,
                        'take_profit_pips': 100,
                        'min_confidence': 0.3,
                        'cliff_brake_enabled': True,
                        'trailing_stop_enabled': False,
                        'trailing_stop_distance': 20,
                        'trailing_stop_step': 10
                    },
                    'backtest_result': {
                        'statistics': {
                            'gross_profit': 1250.0,
                            'gross_loss': -500.0,
                            'net_profit': 750.0,
                            'profit_factor': 2.5,
                            'average_win': 96.15,
                            'average_loss': -71.43,
                            'max_win': 200.0,
                            'max_loss': -150.0,
                            'max_consecutive_wins': 5,
                            'max_consecutive_losses': 3
                        }
                    }
                }
            ],
            'date_range': {
                'start_date': '2024-01-01',
                'end_date': '2024-01-07'
            }
        }
        
        # 模拟保存结果到数据库（实际测试中可能需要真实数据）
        print("✅ CSV导出格式测试准备完成")
        print("💡 实际导出测试需要在有真实优化结果的情况下进行")
        
    except Exception as e:
        print(f"❌ CSV导出格式测试失败: {e}")

if __name__ == "__main__":
    test_parameter_optimization_improvements()
    test_csv_export_format()
