#!/usr/bin/env python3
"""
测试最终参数范围设置
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.deep_learning_service import DeepLearningService

def test_final_parameter_ranges():
    """测试最终参数范围设置"""
    print("🎯 测试最终参数范围设置")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 生成参数组合
        print("🔄 生成参数组合...")
        combinations = dl_service._generate_parameter_combinations()
        
        if not combinations:
            print("❌ 无法生成参数组合")
            return False
        
        print(f"✅ 成功生成 {len(combinations):,} 个参数组合")
        
        # 检查各参数范围
        lot_sizes = set()
        stop_losses = set()
        take_profits = set()
        confidences = set()
        
        for combo in combinations:
            lot_sizes.add(combo['lot_size'])
            stop_losses.add(combo['stop_loss_pips'])
            take_profits.add(combo['take_profit_pips'])
            confidences.add(combo['min_confidence'])
        
        # 期望值
        expected_lot_sizes = {0.01}
        expected_stop_losses = {50, 70, 80}
        expected_take_profits = {60, 80, 100, 150}
        expected_confidences = {0.30, 0.40, 0.50, 0.60, 0.70, 0.80}
        
        print(f"\n📊 参数范围验证:")
        
        # 验证手数
        print(f"   手数范围:")
        print(f"     期望: {sorted(expected_lot_sizes)}")
        print(f"     实际: {sorted(lot_sizes)}")
        lot_ok = lot_sizes == expected_lot_sizes
        print(f"     结果: {'✅ 正确' if lot_ok else '❌ 错误'}")
        
        # 验证止损
        print(f"   止损范围:")
        print(f"     期望: {sorted(expected_stop_losses)} pips")
        print(f"     实际: {sorted(stop_losses)} pips")
        stop_ok = stop_losses == expected_stop_losses
        print(f"     结果: {'✅ 正确' if stop_ok else '❌ 错误'}")
        
        # 验证止盈
        print(f"   止盈范围:")
        print(f"     期望: {sorted(expected_take_profits)} pips")
        print(f"     实际: {sorted(take_profits)} pips")
        profit_ok = take_profits == expected_take_profits
        print(f"     结果: {'✅ 正确' if profit_ok else '❌ 错误'}")
        
        # 验证置信度
        print(f"   置信度范围:")
        print(f"     期望: {sorted(expected_confidences)}")
        print(f"     实际: {sorted(confidences)}")
        conf_ok = confidences == expected_confidences
        print(f"     结果: {'✅ 正确' if conf_ok else '❌ 错误'}")
        
        return all([lot_ok, stop_ok, profit_ok, conf_ok])
        
    except Exception as e:
        print(f"❌ 参数范围测试失败: {e}")
        return False

def test_combination_count_analysis():
    """测试组合数量分析"""
    print("\n🧮 测试组合数量分析")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 计算理论组合数
        parameter_counts = {
            'lot_size': 1,  # [0.01] - 固定
            'stop_loss_pips': 3,  # [50, 70, 80]
            'take_profit_pips': 4,  # [60, 80, 100, 150]
            'min_confidence': 6,  # [0.30, 0.40, 0.50, 0.60, 0.70, 0.80]
            'cliff_brake_enabled': 2,  # [False, True]
            'trailing_stop_enabled': 2,  # [False, True]
            'trailing_stop_distance': 4,  # [15, 20, 25, 30]
            'trailing_stop_step': 4  # [5, 10, 15, 20]
        }
        
        print("📊 参数范围统计:")
        theoretical_total = 1
        for param, count in parameter_counts.items():
            theoretical_total *= count
            print(f"   {param}: {count} 个值")
        
        print(f"\n🔢 理论总组合数: {theoretical_total:,}")
        
        # 生成实际组合
        combinations = dl_service._generate_parameter_combinations()
        actual_total = len(combinations)
        
        print(f"📈 实际组合数: {actual_total:,}")
        
        if actual_total > 0:
            if actual_total == theoretical_total:
                print("🔍 过滤率: 0.0% (无过滤)")
            else:
                filter_rate = (theoretical_total - actual_total) / theoretical_total * 100
                print(f"🔍 过滤率: {filter_rate:.1f}%")
            
            # 估算优化时间
            estimated_time = actual_total * 0.5 / 60  # 分钟
            print(f"⏱️ 估算优化时间: {estimated_time:.1f} 分钟")
            
            # 评估时间合理性
            if estimated_time < 15:
                time_status = "✅ 优秀"
            elif estimated_time < 30:
                time_status = "✅ 良好"
            elif estimated_time < 60:
                time_status = "⚠️ 可接受"
            else:
                time_status = "⚠️ 较长"
            
            print(f"   时间评价: {time_status}")
            
            return True
        else:
            print("❌ 无法生成参数组合")
            return False
            
    except Exception as e:
        print(f"❌ 组合数量分析失败: {e}")
        return False

def test_parameter_distribution():
    """测试参数分布"""
    print("\n📊 测试参数分布")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 生成参数组合
        combinations = dl_service._generate_parameter_combinations()
        
        if not combinations:
            print("❌ 无法生成参数组合")
            return False
        
        # 统计各参数的分布
        stop_loss_count = {}
        take_profit_count = {}
        confidence_count = {}
        
        for combo in combinations:
            # 止损分布
            sl = combo['stop_loss_pips']
            stop_loss_count[sl] = stop_loss_count.get(sl, 0) + 1
            
            # 止盈分布
            tp = combo['take_profit_pips']
            take_profit_count[tp] = take_profit_count.get(tp, 0) + 1
            
            # 置信度分布
            conf = combo['min_confidence']
            confidence_count[conf] = confidence_count.get(conf, 0) + 1
        
        total_combinations = len(combinations)
        
        print("📈 参数分布统计:")
        
        # 止损分布
        print(f"\n   止损点数分布:")
        for sl in sorted(stop_loss_count.keys()):
            count = stop_loss_count[sl]
            percentage = (count / total_combinations) * 100
            print(f"     {sl} pips: {count:,} 组合 ({percentage:.1f}%)")
        
        # 止盈分布
        print(f"\n   止盈点数分布:")
        for tp in sorted(take_profit_count.keys()):
            count = take_profit_count[tp]
            percentage = (count / total_combinations) * 100
            print(f"     {tp} pips: {count:,} 组合 ({percentage:.1f}%)")
        
        # 置信度分布
        print(f"\n   置信度分布:")
        for conf in sorted(confidence_count.keys()):
            count = confidence_count[conf]
            percentage = (count / total_combinations) * 100
            print(f"     {conf:.2f}: {count:,} 组合 ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数分布测试失败: {e}")
        return False

def test_risk_reward_ratios():
    """测试风险收益比分析"""
    print("\n⚖️ 测试风险收益比分析")
    print("=" * 60)
    
    try:
        dl_service = DeepLearningService()
        
        # 生成参数组合
        combinations = dl_service._generate_parameter_combinations()
        
        if not combinations:
            print("❌ 无法生成参数组合")
            return False
        
        # 计算风险收益比
        risk_reward_analysis = {}
        
        for combo in combinations:
            sl = combo['stop_loss_pips']
            tp = combo['take_profit_pips']
            
            # 计算风险收益比 (止盈/止损)
            risk_reward_ratio = tp / sl
            ratio_key = f"{tp}:{sl}"
            
            if ratio_key not in risk_reward_analysis:
                risk_reward_analysis[ratio_key] = {
                    'ratio': risk_reward_ratio,
                    'count': 0,
                    'stop_loss': sl,
                    'take_profit': tp
                }
            
            risk_reward_analysis[ratio_key]['count'] += 1
        
        print("📊 风险收益比分析:")
        print("   格式: 止盈:止损 (比例) - 组合数量")
        
        # 按风险收益比排序
        sorted_ratios = sorted(risk_reward_analysis.items(), 
                             key=lambda x: x[1]['ratio'])
        
        for ratio_key, data in sorted_ratios:
            ratio = data['ratio']
            count = data['count']
            percentage = (count / len(combinations)) * 100
            
            # 评估风险收益比
            if ratio >= 2.0:
                rating = "🟢 优秀"
            elif ratio >= 1.5:
                rating = "🟡 良好"
            elif ratio >= 1.0:
                rating = "🟠 一般"
            else:
                rating = "🔴 较差"
            
            print(f"   {ratio_key} ({ratio:.2f}) - {count:,} 组合 ({percentage:.1f}%) {rating}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险收益比分析失败: {e}")
        return False

def test_parameter_validation():
    """测试参数有效性验证"""
    print("\n✅ 测试参数有效性验证")
    print("=" * 60)
    
    print("🎯 参数设置合理性分析:")
    
    # 手数设置
    print("\n   💰 手数设置 (0.01):")
    print("     ✅ 固定为0.01手，适合小资金账户")
    print("     ✅ 降低单笔交易风险")
    print("     ✅ 适合新手和保守交易者")
    
    # 止损设置
    print("\n   🛡️ 止损设置 [50, 70, 80] pips:")
    print("     ✅ 50 pips - 紧密止损，适合短线交易")
    print("     ✅ 70 pips - 中等止损，平衡保护与空间")
    print("     ✅ 80 pips - 宽松止损，给予更多波动空间")
    
    # 止盈设置
    print("\n   🎯 止盈设置 [60, 80, 100, 150] pips:")
    print("     ✅ 60 pips - 保守目标，快速获利")
    print("     ✅ 80 pips - 平衡目标，适中收益")
    print("     ✅ 100 pips - 积极目标，较高收益")
    print("     ✅ 150 pips - 激进目标，追求大收益")
    
    # 置信度设置
    print("\n   🎲 置信度设置 [0.30-0.80]:")
    print("     ✅ 0.30-0.40 - 更多交易机会，需要严格风控")
    print("     ✅ 0.50-0.60 - 平衡交易频率与质量")
    print("     ✅ 0.70-0.80 - 高质量信号，低频交易")
    
    # 风险收益比分析
    print("\n   ⚖️ 风险收益比范围:")
    ratios = []
    for tp in [60, 80, 100, 150]:
        for sl in [50, 70, 80]:
            ratio = tp / sl
            ratios.append(f"{tp}:{sl} ({ratio:.2f})")
    
    print(f"     范围: {min([60/80, 80/80, 100/80, 150/50]):.2f} - {max([60/50, 80/50, 100/50, 150/50]):.2f}")
    print("     ✅ 大部分组合风险收益比 > 1.0")
    print("     ✅ 最佳组合风险收益比可达 3.0")
    
    return True

def main():
    print("🎯 最终参数范围设置测试")
    print("=" * 80)
    print("📊 参数设置:")
    print("   • 手数范围: [0.01] - 固定")
    print("   • 止损范围: [50, 70, 80] pips - 3个选择")
    print("   • 止盈范围: [60, 80, 100, 150] pips - 4个选择")
    print("   • 置信度范围: [0.30, 0.40, 0.50, 0.60, 0.70, 0.80] - 6个选择")
    print("=" * 80)
    
    # 测试1: 参数范围验证
    ranges_ok = test_final_parameter_ranges()
    
    # 测试2: 组合数量分析
    count_ok = test_combination_count_analysis()
    
    # 测试3: 参数分布
    distribution_ok = test_parameter_distribution()
    
    # 测试4: 风险收益比分析
    risk_reward_ok = test_risk_reward_ratios()
    
    # 测试5: 参数有效性验证
    validation_ok = test_parameter_validation()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    print(f"参数范围验证: {'✅ 通过' if ranges_ok else '❌ 失败'}")
    print(f"组合数量分析: {'✅ 通过' if count_ok else '❌ 失败'}")
    print(f"参数分布测试: {'✅ 通过' if distribution_ok else '❌ 失败'}")
    print(f"风险收益比分析: {'✅ 通过' if risk_reward_ok else '❌ 失败'}")
    print(f"参数有效性验证: {'✅ 通过' if validation_ok else '❌ 失败'}")
    
    if all([ranges_ok, count_ok, distribution_ok, risk_reward_ok, validation_ok]):
        print("\n🎉 最终参数范围设置完成！")
        print("\n✅ 设置特点:")
        print("   • 手数固定为0.01，降低风险")
        print("   • 止损范围合理，适应不同策略")
        print("   • 止盈范围多样，满足不同目标")
        print("   • 置信度精细，灵活筛选信号")
        
        print("\n📊 优化效果:")
        print("   • 参数组合数量适中，优化时间合理")
        print("   • 风险收益比分布良好")
        print("   • 适合不同风险偏好的交易者")
        print("   • 参数设置科学合理")
        
        print("\n🚀 适用场景:")
        print("   • 小资金账户交易")
        print("   • 新手交易者学习")
        print("   • 保守稳健策略")
        print("   • 参数优化和回测")
        
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == '__main__':
    main()
